{"name": "safeverse", "version": "1.0.0", "description": "AI-Powered AR/VR Safety Training & Assistance Bot", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "build": "webpack --mode production", "lint": "eslint src/", "format": "prettier --write src/"}, "keywords": ["AR", "VR", "AI", "Safety", "Training", "<PERSON><PERSON><PERSON>", "Unity", "IoT"], "author": "SafeVerse Development Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "mongoose": "^7.5.0", "firebase-admin": "^11.10.1", "openai": "^4.0.0", "dialogflow": "^6.0.0", "mqtt": "^5.0.0", "ws": "^8.13.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "eslint": "^8.47.0", "prettier": "^3.0.2", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}