import * as v2 from './v2';
import * as v2beta1 from './v2beta1';
declare const AgentsClient: typeof v2.AgentsClient;
type AgentsClient = v2.AgentsClient;
declare const AnswerRecordsClient: typeof v2.AnswerRecordsClient;
type AnswerRecordsClient = v2.AnswerRecordsClient;
declare const ContextsClient: typeof v2.ContextsClient;
type ContextsClient = v2.ContextsClient;
declare const ConversationDatasetsClient: typeof v2.ConversationDatasetsClient;
type ConversationDatasetsClient = v2.ConversationDatasetsClient;
declare const ConversationModelsClient: typeof v2.ConversationModelsClient;
type ConversationModelsClient = v2.ConversationModelsClient;
declare const ConversationProfilesClient: typeof v2.ConversationProfilesClient;
type ConversationProfilesClient = v2.ConversationProfilesClient;
declare const ConversationsClient: typeof v2.ConversationsClient;
type ConversationsClient = v2.ConversationsClient;
declare const DocumentsClient: typeof v2.DocumentsClient;
type DocumentsClient = v2.DocumentsClient;
declare const EncryptionSpecServiceClient: typeof v2.EncryptionSpecServiceClient;
type EncryptionSpecServiceClient = v2.EncryptionSpecServiceClient;
declare const EntityTypesClient: typeof v2.EntityTypesClient;
type EntityTypesClient = v2.EntityTypesClient;
declare const EnvironmentsClient: typeof v2.EnvironmentsClient;
type EnvironmentsClient = v2.EnvironmentsClient;
declare const FulfillmentsClient: typeof v2.FulfillmentsClient;
type FulfillmentsClient = v2.FulfillmentsClient;
declare const GeneratorsClient: typeof v2.GeneratorsClient;
type GeneratorsClient = v2.GeneratorsClient;
declare const IntentsClient: typeof v2.IntentsClient;
type IntentsClient = v2.IntentsClient;
declare const KnowledgeBasesClient: typeof v2.KnowledgeBasesClient;
type KnowledgeBasesClient = v2.KnowledgeBasesClient;
declare const ParticipantsClient: typeof v2.ParticipantsClient;
type ParticipantsClient = v2.ParticipantsClient;
declare const SessionEntityTypesClient: typeof v2.SessionEntityTypesClient;
type SessionEntityTypesClient = v2.SessionEntityTypesClient;
declare const SessionsClient: typeof v2.SessionsClient;
type SessionsClient = v2.SessionsClient;
declare const VersionsClient: typeof v2.VersionsClient;
type VersionsClient = v2.VersionsClient;
export { v2, v2beta1, AgentsClient, AnswerRecordsClient, ContextsClient, ConversationDatasetsClient, ConversationModelsClient, ConversationProfilesClient, ConversationsClient, DocumentsClient, EncryptionSpecServiceClient, EntityTypesClient, EnvironmentsClient, FulfillmentsClient, GeneratorsClient, IntentsClient, KnowledgeBasesClient, ParticipantsClient, SessionEntityTypesClient, SessionsClient, VersionsClient, };
declare const _default: {
    v2: typeof v2;
    v2beta1: typeof v2beta1;
    AgentsClient: typeof v2.AgentsClient;
    AnswerRecordsClient: typeof v2.AnswerRecordsClient;
    ContextsClient: typeof v2.ContextsClient;
    ConversationDatasetsClient: typeof v2.ConversationDatasetsClient;
    ConversationModelsClient: typeof v2.ConversationModelsClient;
    ConversationProfilesClient: typeof v2.ConversationProfilesClient;
    ConversationsClient: typeof v2.ConversationsClient;
    DocumentsClient: typeof v2.DocumentsClient;
    EncryptionSpecServiceClient: typeof v2.EncryptionSpecServiceClient;
    EntityTypesClient: typeof v2.EntityTypesClient;
    EnvironmentsClient: typeof v2.EnvironmentsClient;
    FulfillmentsClient: typeof v2.FulfillmentsClient;
    GeneratorsClient: typeof v2.GeneratorsClient;
    IntentsClient: typeof v2.IntentsClient;
    KnowledgeBasesClient: typeof v2.KnowledgeBasesClient;
    ParticipantsClient: typeof v2.ParticipantsClient;
    SessionEntityTypesClient: typeof v2.SessionEntityTypesClient;
    SessionsClient: typeof v2.SessionsClient;
    VersionsClient: typeof v2.VersionsClient;
};
export default _default;
import * as protos from '../protos/protos';
export { protos };
