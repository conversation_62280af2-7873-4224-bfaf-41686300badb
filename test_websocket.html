<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SafeVerse WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #34495e;
            margin-top: 0;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        .output {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .connected {
            background: #d5f4e6;
            color: #27ae60;
        }
        .disconnected {
            background: #fadbd8;
            color: #e74c3c;
        }
        input[type="text"] {
            width: 70%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌟 SafeVerse WebSocket Test Client</h1>
            <p>Test real-time communication with SafeVerse AI-Powered AR/VR Safety Training platform</p>
        </div>

        <div id="status" class="status disconnected">
            Status: Disconnected
        </div>

        <div class="test-section">
            <h3>🤖 AI Chatbot Test</h3>
            <input type="text" id="chatMessage" placeholder="Ask a safety question..." value="How do I handle a fire emergency?">
            <button onclick="testChatbot()">Send Message</button>
            <div id="chatOutput" class="output"></div>
        </div>

        <div class="test-section">
            <h3>📱 AR Safety Scanner Test</h3>
            <button onclick="testARScan()">Simulate AR Scan</button>
            <div id="arOutput" class="output"></div>
        </div>

        <div class="test-section">
            <h3>🥽 VR Training Test</h3>
            <button onclick="testVRSession()">Start VR Training</button>
            <div id="vrOutput" class="output"></div>
        </div>

        <div class="test-section">
            <h3>🎤 Voice Command Test</h3>
            <button onclick="testVoiceCommand()">Test Emergency Voice Command</button>
            <div id="voiceOutput" class="output"></div>
        </div>

        <div class="test-section">
            <h3>📡 IoT Sensor Test</h3>
            <button onclick="testIoTSensor()">Send Critical Temperature Alert</button>
            <div id="iotOutput" class="output"></div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        let socket;
        
        function connect() {
            socket = io('http://localhost:3000');
            
            socket.on('connect', () => {
                updateStatus('Connected', true);
                log('System', 'Connected to SafeVerse server');
            });
            
            socket.on('disconnect', () => {
                updateStatus('Disconnected', false);
                log('System', 'Disconnected from SafeVerse server');
            });
            
            socket.on('chatbot-response', (response) => {
                log('chatOutput', `AI Response: ${response.response}\nConfidence: ${response.confidence}\nSuggestions: ${response.suggestions.join(', ')}`);
            });
            
            socket.on('ar-analysis', (analysis) => {
                log('arOutput', `AR Analysis Complete!\nSafety Score: ${analysis.safety_score}\nHazards Found: ${analysis.detected_hazards.length}\nPriority: ${analysis.priority_level}`);
            });
            
            socket.on('vr-session-started', (session) => {
                log('vrOutput', `VR Session Started!\nScenario: ${session.scenario.title}\nSession ID: ${session.sessionId}\nObjectives: ${session.scenario.objectives.length}`);
            });
            
            socket.on('voice-response', (result) => {
                log('voiceOutput', `Voice Command Processed!\nCommand: ${result.recognized_command.type}\nResponse: ${result.response.message}`);
            });
            
            socket.on('safety-alert', (alert) => {
                log('iotOutput', `🚨 SAFETY ALERT!\nType: ${alert.type}\nLevel: ${alert.level}\nMessage: ${alert.message}\nRecommendations: ${alert.recommendations.join(', ')}`);
            });
            
            socket.on('error', (error) => {
                log('System', `Error: ${error.message}`);
            });
        }
        
        function updateStatus(status, connected) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = `Status: ${status}`;
            statusEl.className = `status ${connected ? 'connected' : 'disconnected'}`;
        }
        
        function log(outputId, message) {
            const output = document.getElementById(outputId);
            if (output) {
                output.textContent += `[${new Date().toLocaleTimeString()}] ${message}\n`;
                output.scrollTop = output.scrollHeight;
            }
        }
        
        function testChatbot() {
            const message = document.getElementById('chatMessage').value;
            socket.emit('chatbot-message', {
                message: message,
                userId: 'test_user',
                context: {
                    environment: 'office',
                    platform: 'web'
                }
            });
            log('chatOutput', `You: ${message}`);
        }
        
        function testARScan() {
            socket.emit('ar-scan', {
                imageData: 'mock_base64_image_data',
                location: {
                    building: 'office_building_a',
                    floor: 2
                },
                timestamp: new Date().toISOString(),
                deviceInfo: {
                    type: 'mobile',
                    model: 'Test Device'
                }
            });
            log('arOutput', 'AR scan initiated...');
        }
        
        function testVRSession() {
            socket.emit('vr-session-start', {
                userId: 'test_user',
                scenarioId: 'fire_emergency',
                difficulty: 'beginner'
            });
            log('vrOutput', 'Starting VR training session...');
        }
        
        function testVoiceCommand() {
            socket.emit('voice-command', {
                transcript: 'Emergency help needed',
                userId: 'test_user',
                context: {
                    environment: 'factory'
                }
            });
            log('voiceOutput', 'Voice command sent: "Emergency help needed"');
        }
        
        function testIoTSensor() {
            socket.emit('iot-data', {
                deviceId: 'sensor_001',
                sensorType: 'temperature',
                value: 50.0,
                timestamp: new Date().toISOString(),
                location: {
                    building: 'factory_a',
                    floor: 1
                }
            });
            log('iotOutput', 'IoT sensor data sent: Temperature 50°C (Critical)');
        }
        
        // Connect when page loads
        window.onload = connect;
    </script>
</body>
</html>
