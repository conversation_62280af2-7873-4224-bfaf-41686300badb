# Unity Project Settings for SafeVerse

## Unity Version
- **Recommended**: Unity 2022.3 LTS
- **Minimum**: Unity 2021.3 LTS

## Required Unity Packages

### AR/VR Packages
- AR Foundation (com.unity.xr.arfoundation)
- ARCore XR Plugin (com.unity.xr.arcore)
- ARKit XR Plugin (com.unity.xr.arkit)
- Oculus XR Plugin (com.unity.xr.oculus)
- OpenXR Plugin (com.unity.xr.openxr)
- XR Interaction Toolkit (com.unity.xr.interaction.toolkit)

### AI and Networking
- Unity Netcode for GameObjects (com.unity.netcode.gameobjects)
- Unity Cloud Build (com.unity.cloud.build)
- Unity Analytics (com.unity.analytics)

### Audio and Input
- Input System (com.unity.inputsystem)
- Audio (com.unity.modules.audio)
- Microphone (com.unity.modules.audio)

### Development Tools
- Visual Scripting (com.unity.visualscripting)
- Test Framework (com.unity.test-framework)
- Unity Collaborate (com.unity.collab-proxy)

## Platform Settings

### Android (AR Mobile)
- **Minimum API Level**: 24 (Android 7.0)
- **Target API Level**: 33 (Android 13)
- **Architecture**: ARM64
- **Graphics API**: Vulkan, OpenGLES3

### iOS (AR Mobile)
- **Minimum iOS Version**: 12.0
- **Target iOS Version**: 16.0
- **Architecture**: ARM64
- **Graphics API**: Metal

### Windows (VR Desktop)
- **Minimum Windows Version**: Windows 10
- **Graphics API**: DirectX 11/12
- **Architecture**: x64

## Build Configurations

### Development Build
- Development Build: Enabled
- Script Debugging: Enabled
- Deep Profiling: Enabled

### Release Build
- Development Build: Disabled
- Script Debugging: Disabled
- Optimization: Size
- Stripping Level: Medium

## Quality Settings
- **Mobile**: Medium quality for AR applications
- **Desktop VR**: High quality for immersive experience
- **Texture Quality**: Full Res
- **Anti-Aliasing**: 4x Multi Sampling

## XR Settings
- **Initialize XR on Startup**: Enabled
- **Virtual Reality Supported**: Enabled
- **Stereo Rendering Mode**: Single Pass Instanced
