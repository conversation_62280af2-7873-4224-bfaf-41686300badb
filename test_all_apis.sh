#!/bin/bash

# SafeVerse API Test Suite
# Comprehensive testing of all SafeVerse modules

echo "🌟 SafeVerse API Test Suite"
echo "=========================="
echo ""

BASE_URL="http://localhost:3000"
USER_ID="test_user_$(date +%s)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

# Function to run test
run_test() {
    local test_name="$1"
    local command="$2"
    local expected_pattern="$3"
    
    echo -e "${BLUE}Testing: $test_name${NC}"
    
    response=$(eval "$command" 2>/dev/null)
    exit_code=$?
    
    if [ $exit_code -eq 0 ] && [[ $response == *"$expected_pattern"* ]]; then
        echo -e "${GREEN}✅ PASS${NC}: $test_name"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC}: $test_name"
        echo "Response: $response"
        ((TESTS_FAILED++))
    fi
    echo ""
}

echo "🔍 Testing Server Health..."
run_test "Health Check" \
    "curl -s $BASE_URL/health" \
    '"status":"healthy"'

echo "🏠 Testing Main API Endpoint..."
run_test "Main API Info" \
    "curl -s $BASE_URL/" \
    '"message":"SafeVerse API Server"'

echo "🤖 Testing AI Chatbot Module..."
run_test "Chatbot Fire Safety Query" \
    "curl -s -X POST $BASE_URL/api/chatbot/message -H 'Content-Type: application/json' -d '{\"message\": \"How do I use a fire extinguisher?\", \"userId\": \"$USER_ID\"}'" \
    '"response"'

run_test "Chatbot Emergency Query" \
    "curl -s -X POST $BASE_URL/api/chatbot/message -H 'Content-Type: application/json' -d '{\"message\": \"Emergency help needed\", \"userId\": \"$USER_ID\"}'" \
    '"response"'

run_test "Chatbot Knowledge Base" \
    "curl -s $BASE_URL/api/chatbot/knowledge-base" \
    '"fire_safety"'

echo "📱 Testing AR Safety Scanner Module..."
run_test "AR Safety Scan" \
    "curl -s -X POST $BASE_URL/api/ar/scan -H 'Content-Type: application/json' -d '{\"imageData\": \"test_image\", \"location\": {\"building\": \"office_a\"}}'" \
    '"analysis_id"'

run_test "AR Hazard Models" \
    "curl -s $BASE_URL/api/ar/hazard-models" \
    '"fire_hazards"'

run_test "AR Safety Standards" \
    "curl -s $BASE_URL/api/ar/safety-standards" \
    '"OSHA"'

echo "🥽 Testing VR Training Module..."
run_test "VR Fire Emergency Training" \
    "curl -s -X POST $BASE_URL/api/vr/start-session -H 'Content-Type: application/json' -d '{\"userId\": \"$USER_ID\", \"scenarioId\": \"fire_emergency\"}'" \
    '"sessionId"'

run_test "VR Workplace Safety Training" \
    "curl -s -X POST $BASE_URL/api/vr/start-session -H 'Content-Type: application/json' -d '{\"userId\": \"$USER_ID\", \"scenarioId\": \"workplace_safety\"}'" \
    '"sessionId"'

run_test "VR Available Scenarios" \
    "curl -s $BASE_URL/api/vr/scenarios" \
    '"fire_emergency"'

echo "🎤 Testing Voice & Gesture Module..."
run_test "Voice Emergency Command" \
    "curl -s -X POST $BASE_URL/api/voice-gesture/voice-command -H 'Content-Type: application/json' -d '{\"transcript\": \"Emergency help\", \"userId\": \"$USER_ID\"}'" \
    '"recognized_command"'

run_test "Voice Safety Query" \
    "curl -s -X POST $BASE_URL/api/voice-gesture/voice-command -H 'Content-Type: application/json' -d '{\"transcript\": \"How to use fire extinguisher\", \"userId\": \"$USER_ID\"}'" \
    '"recognized_command"'

run_test "Voice Commands List" \
    "curl -s $BASE_URL/api/voice-gesture/commands" \
    '"emergency"'

echo "📡 Testing IoT Integration Module..."
run_test "IoT Normal Temperature" \
    "curl -s -X POST $BASE_URL/api/iot/sensor-data -H 'Content-Type: application/json' -d '{\"deviceId\": \"sensor_001\", \"sensorType\": \"temperature\", \"value\": 22.0}'" \
    '"status":"processed"'

run_test "IoT Critical Temperature Alert" \
    "curl -s -X POST $BASE_URL/api/iot/sensor-data -H 'Content-Type: application/json' -d '{\"deviceId\": \"sensor_002\", \"sensorType\": \"temperature\", \"value\": 45.0}'" \
    '"alert"'

run_test "IoT Gas Concentration Alert" \
    "curl -s -X POST $BASE_URL/api/iot/sensor-data -H 'Content-Type: application/json' -d '{\"deviceId\": \"sensor_003\", \"sensorType\": \"gas_concentration\", \"value\": 60.0}'" \
    '"alert"'

run_test "IoT Connected Devices" \
    "curl -s $BASE_URL/api/iot/devices" \
    '"sensor_001"'

run_test "IoT Sensor Thresholds" \
    "curl -s $BASE_URL/api/iot/thresholds" \
    '"temperature"'

echo "🎮 Testing Gamification Module..."
run_test "User Profile Creation" \
    "curl -s $BASE_URL/api/gamification/profile/$USER_ID" \
    '"id"'

run_test "Training Completion Progress" \
    "curl -s -X POST $BASE_URL/api/gamification/progress/$USER_ID -H 'Content-Type: application/json' -d '{\"type\": \"training_completed\", \"data\": {\"sessionId\": \"session_123\", \"score\": 95, \"module\": \"fire_safety\"}}'" \
    '"levelUp"'

run_test "Hazard Detection Progress" \
    "curl -s -X POST $BASE_URL/api/gamification/progress/$USER_ID -H 'Content-Type: application/json' -d '{\"type\": \"hazard_detected\", \"data\": {\"hazardType\": \"blocked_exit\"}}'" \
    '"levelUp"'

run_test "Overall Leaderboard" \
    "curl -s $BASE_URL/api/gamification/leaderboard/overall" \
    '"rank"'

run_test "Points Leaderboard" \
    "curl -s $BASE_URL/api/gamification/leaderboard/points" \
    '"rank"'

run_test "Available Achievements" \
    "curl -s $BASE_URL/api/gamification/achievements" \
    '"first_training"'

echo "🔄 Testing Advanced Scenarios..."
run_test "Multiple Sensor Alerts" \
    "curl -s -X POST $BASE_URL/api/iot/sensor-data -H 'Content-Type: application/json' -d '{\"deviceId\": \"sensor_004\", \"sensorType\": \"air_quality\", \"value\": 150}'" \
    '"alert"'

run_test "Perfect Score Achievement" \
    "curl -s -X POST $BASE_URL/api/gamification/progress/$USER_ID -H 'Content-Type: application/json' -d '{\"type\": \"training_completed\", \"data\": {\"sessionId\": \"session_perfect\", \"score\": 100, \"module\": \"fire_safety\", \"perfectScore\": true}}'" \
    '"levelUp"'

echo ""
echo "📊 Test Results Summary"
echo "======================"
echo -e "${GREEN}Tests Passed: $TESTS_PASSED${NC}"
echo -e "${RED}Tests Failed: $TESTS_FAILED${NC}"
echo -e "Total Tests: $((TESTS_PASSED + TESTS_FAILED))"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "${GREEN}🎉 All tests passed! SafeVerse is working perfectly!${NC}"
    exit 0
else
    echo -e "${YELLOW}⚠️  Some tests failed. Please check the output above.${NC}"
    exit 1
fi
