/**
 * Gamification Module
 * Handles scoring, achievements, and progress tracking
 */

const express = require('express');
const router = express.Router();

class GamificationSystem {
  constructor() {
    this.initialized = false;
    this.userProfiles = new Map();
    this.achievements = new Map();
    this.leaderboards = new Map();
    this.challenges = new Map();
    this.init();
  }

  async init() {
    try {
      console.log('Initializing Gamification System...');
      await this.setupAchievements();
      await this.setupChallenges();
      this.initialized = true;
      console.log('Gamification System initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Gamification System:', error);
    }
  }

  async setupAchievements() {
    this.achievements.set('first_training', {
      id: 'first_training',
      title: 'Safety Rookie',
      description: 'Complete your first safety training session',
      icon: '🎓',
      points: 100,
      category: 'training'
    });

    this.achievements.set('fire_expert', {
      id: 'fire_expert',
      title: 'Fire Safety Expert',
      description: 'Complete 5 fire safety training sessions with 90+ score',
      icon: '🔥',
      points: 500,
      category: 'expertise'
    });

    this.achievements.set('perfect_score', {
      id: 'perfect_score',
      title: 'Perfect Performance',
      description: 'Achieve a perfect score in any training session',
      icon: '⭐',
      points: 300,
      category: 'performance'
    });

    this.achievements.set('safety_streak', {
      id: 'safety_streak',
      title: 'Safety Streak',
      description: 'Complete training sessions for 7 consecutive days',
      icon: '🔥',
      points: 400,
      category: 'consistency'
    });

    this.achievements.set('hazard_hunter', {
      id: 'hazard_hunter',
      title: 'Hazard Hunter',
      description: 'Identify 50 safety hazards using AR scanner',
      icon: '🔍',
      points: 250,
      category: 'detection'
    });
  }

  async setupChallenges() {
    this.challenges.set('weekly_training', {
      id: 'weekly_training',
      title: 'Weekly Safety Challenge',
      description: 'Complete 3 different training modules this week',
      duration: 7 * 24 * 60 * 60 * 1000, // 7 days
      reward: 200,
      requirements: {
        training_sessions: 3,
        different_modules: true
      }
    });

    this.challenges.set('ar_scanning_challenge', {
      id: 'ar_scanning_challenge',
      title: 'AR Safety Scanner Challenge',
      description: 'Scan 10 different environments for safety hazards',
      duration: 30 * 24 * 60 * 60 * 1000, // 30 days
      reward: 300,
      requirements: {
        ar_scans: 10,
        different_environments: true
      }
    });
  }

  async getUserProfile(userId) {
    if (!this.userProfiles.has(userId)) {
      this.userProfiles.set(userId, {
        id: userId,
        level: 1,
        totalPoints: 0,
        achievements: [],
        completedTrainings: [],
        currentStreak: 0,
        longestStreak: 0,
        statistics: {
          totalTrainingSessions: 0,
          averageScore: 0,
          totalTimeSpent: 0,
          hazardsDetected: 0,
          perfectScores: 0
        },
        activeChallenges: [],
        joinDate: new Date()
      });
    }
    return this.userProfiles.get(userId);
  }

  async updateUserProgress(userId, progressData) {
    const profile = await this.getUserProfile(userId);
    const { type, data } = progressData;

    switch (type) {
      case 'training_completed':
        await this.handleTrainingCompletion(profile, data);
        break;
      case 'hazard_detected':
        await this.handleHazardDetection(profile, data);
        break;
      case 'ar_scan_completed':
        await this.handleARScanCompletion(profile, data);
        break;
    }

    // Update level based on total points
    const newLevel = this.calculateLevel(profile.totalPoints);
    if (newLevel > profile.level) {
      profile.level = newLevel;
      return { levelUp: true, newLevel, rewards: this.getLevelUpRewards(newLevel) };
    }

    return { levelUp: false };
  }

  async handleTrainingCompletion(profile, data) {
    const { sessionId, score, duration, module, perfectScore } = data;
    
    profile.statistics.totalTrainingSessions++;
    profile.statistics.totalTimeSpent += duration;
    
    // Update average score
    const totalScore = profile.statistics.averageScore * (profile.statistics.totalTrainingSessions - 1) + score;
    profile.statistics.averageScore = totalScore / profile.statistics.totalTrainingSessions;

    // Add points based on score
    const points = Math.floor(score * 2); // 2 points per score point
    profile.totalPoints += points;

    // Check for achievements
    if (profile.statistics.totalTrainingSessions === 1) {
      await this.awardAchievement(profile, 'first_training');
    }

    if (perfectScore) {
      profile.statistics.perfectScores++;
      await this.awardAchievement(profile, 'perfect_score');
    }

    // Update streak
    const today = new Date().toDateString();
    const lastTraining = profile.completedTrainings[profile.completedTrainings.length - 1];
    
    if (!lastTraining || lastTraining.date !== today) {
      profile.currentStreak++;
      profile.longestStreak = Math.max(profile.longestStreak, profile.currentStreak);
      
      if (profile.currentStreak >= 7) {
        await this.awardAchievement(profile, 'safety_streak');
      }
    }

    profile.completedTrainings.push({
      sessionId,
      module,
      score,
      duration,
      date: today,
      timestamp: new Date()
    });

    // Check module-specific achievements
    const moduleCompletions = profile.completedTrainings.filter(t => t.module === 'fire_safety' && t.score >= 90);
    if (moduleCompletions.length >= 5) {
      await this.awardAchievement(profile, 'fire_expert');
    }
  }

  async handleHazardDetection(profile, data) {
    profile.statistics.hazardsDetected++;
    
    if (profile.statistics.hazardsDetected >= 50) {
      await this.awardAchievement(profile, 'hazard_hunter');
    }

    // Award points for hazard detection
    profile.totalPoints += 10;
  }

  async handleARScanCompletion(profile, data) {
    // Award points for AR scanning
    profile.totalPoints += 20;
  }

  async awardAchievement(profile, achievementId) {
    if (profile.achievements.includes(achievementId)) {
      return; // Already awarded
    }

    const achievement = this.achievements.get(achievementId);
    if (achievement) {
      profile.achievements.push(achievementId);
      profile.totalPoints += achievement.points;
      
      return {
        achievement,
        newAchievement: true
      };
    }
  }

  calculateLevel(totalPoints) {
    // Level calculation: Level = floor(sqrt(totalPoints / 100)) + 1
    return Math.floor(Math.sqrt(totalPoints / 100)) + 1;
  }

  getLevelUpRewards(level) {
    const rewards = {
      2: { points: 50, title: 'Safety Apprentice' },
      5: { points: 100, title: 'Safety Professional' },
      10: { points: 200, title: 'Safety Expert' },
      20: { points: 500, title: 'Safety Master' }
    };

    return rewards[level] || { points: level * 10, title: `Level ${level} Safety Specialist` };
  }

  async getLeaderboard(category = 'overall', limit = 10) {
    const users = Array.from(this.userProfiles.values());
    
    let sortedUsers;
    switch (category) {
      case 'points':
        sortedUsers = users.sort((a, b) => b.totalPoints - a.totalPoints);
        break;
      case 'level':
        sortedUsers = users.sort((a, b) => b.level - a.level);
        break;
      case 'streak':
        sortedUsers = users.sort((a, b) => b.longestStreak - a.longestStreak);
        break;
      case 'training_sessions':
        sortedUsers = users.sort((a, b) => b.statistics.totalTrainingSessions - a.statistics.totalTrainingSessions);
        break;
      default:
        sortedUsers = users.sort((a, b) => b.totalPoints - a.totalPoints);
    }

    return sortedUsers.slice(0, limit).map((user, index) => ({
      rank: index + 1,
      userId: user.id,
      level: user.level,
      totalPoints: user.totalPoints,
      achievements: user.achievements.length,
      statistic: this.getLeaderboardStatistic(user, category)
    }));
  }

  getLeaderboardStatistic(user, category) {
    switch (category) {
      case 'points':
        return user.totalPoints;
      case 'level':
        return user.level;
      case 'streak':
        return user.longestStreak;
      case 'training_sessions':
        return user.statistics.totalTrainingSessions;
      default:
        return user.totalPoints;
    }
  }
}

const gamificationSystem = new GamificationSystem();

// API Routes
router.get('/profile/:userId', async (req, res) => {
  try {
    const profile = await gamificationSystem.getUserProfile(req.params.userId);
    res.json(profile);
  } catch (error) {
    res.status(500).json({ error: 'Failed to get user profile' });
  }
});

router.post('/progress/:userId', async (req, res) => {
  try {
    const result = await gamificationSystem.updateUserProgress(req.params.userId, req.body);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: 'Failed to update progress' });
  }
});

router.get('/leaderboard/:category?', async (req, res) => {
  try {
    const category = req.params.category || 'overall';
    const limit = parseInt(req.query.limit) || 10;
    const leaderboard = await gamificationSystem.getLeaderboard(category, limit);
    res.json(leaderboard);
  } catch (error) {
    res.status(500).json({ error: 'Failed to get leaderboard' });
  }
});

router.get('/achievements', (req, res) => {
  const achievements = {};
  gamificationSystem.achievements.forEach((value, key) => {
    achievements[key] = value;
  });
  res.json(achievements);
});

module.exports = {
  router,
  updateUserProgress: (userId, data) => gamificationSystem.updateUserProgress(userId, data),
  GamificationSystem
};
