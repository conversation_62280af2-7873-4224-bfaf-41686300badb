# SafeVerse Quick Start Guide

## 🚀 Getting Started in 5 Minutes

### Prerequisites
- Node.js 18+ and npm
- Python 3.8+
- Unity 2022.3 LTS (for AR/VR development)
- Git

### 1. <PERSON><PERSON> and Setup
```bash
git clone <your-repo-url>
cd SafeVerse
chmod +x setup.sh
./setup.sh
```

### 2. Configure Environment
```bash
# Copy and edit environment variables
cp .env.example .env
# Edit .env with your API keys
```

### 3. Start the Server
```bash
npm run dev
```

The server will start at `http://localhost:3000`

### 4. Test the API
```bash
# Health check
curl http://localhost:3000/health

# Test chatbot
curl -X POST http://localhost:3000/api/chatbot/message \
  -H "Content-Type: application/json" \
  -d '{"message": "How do I use a fire extinguisher?", "userId": "test_user"}'
```

## 🎮 Unity Setup

### 1. Create Unity Project
1. Open Unity Hub
2. Create new 3D project in `Unity/SafeVerse/`
3. Import required packages:
   - AR Foundation
   - ARCore XR Plugin (Android)
   - ARKit XR Plugin (iOS)
   - XR Interaction Toolkit
   - Input System

### 2. Configure Build Settings
- **Android**: API Level 24+, ARM64
- **iOS**: iOS 12.0+, ARM64
- **Windows**: DirectX 11/12

### 3. Add SafeVerse Manager
1. Create empty GameObject in scene
2. Add `SafeVerseManager` script
3. Configure server URL and user settings
4. Assign AR/VR components

## 📱 Testing AR Features

### Mobile AR (Android/iOS)
1. Build and deploy to device
2. Point camera at environment
3. Tap "Scan for Hazards" button
4. View AR overlays for detected hazards

### Desktop VR (Oculus/HTC Vive)
1. Connect VR headset
2. Start VR training session
3. Follow AI guidance through scenarios
4. Complete objectives for scoring

## 🔧 Development Workflow

### Backend Development
```bash
# Start development server with hot reload
npm run dev

# Run tests
npm test

# Lint code
npm run lint
```

### Frontend/Unity Development
1. Make changes in Unity
2. Test in Play mode
3. Build and test on target device
4. Use Unity Console for debugging

### AI/ML Development
```bash
# Activate Python environment
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install new packages
pip install package_name
pip freeze > requirements.txt
```

## 🧪 Testing Different Modules

### 1. AI Chatbot
```javascript
// WebSocket test
const socket = io('http://localhost:3000');
socket.emit('chatbot-message', {
  message: 'Emergency help needed',
  userId: 'test_user'
});
```

### 2. AR Safety Scanner
```bash
# Test with sample image
curl -X POST http://localhost:3000/api/ar/scan \
  -H "Content-Type: application/json" \
  -d '{"imageData": "base64_image_data", "location": {"building": "test"}}'
```

### 3. VR Training
```bash
# Start training session
curl -X POST http://localhost:3000/api/vr/start-session \
  -H "Content-Type: application/json" \
  -d '{"userId": "test_user", "scenarioId": "fire_emergency"}'
```

### 4. IoT Integration
```bash
# Send sensor data
curl -X POST http://localhost:3000/api/iot/sensor-data \
  -H "Content-Type: application/json" \
  -d '{"deviceId": "sensor_001", "sensorType": "temperature", "value": 35.5}'
```

## 🎯 Key Features to Test

### AR Safety Scanner
- [ ] Environment scanning
- [ ] Hazard detection
- [ ] AR overlay display
- [ ] Safety recommendations

### VR Training
- [ ] Fire safety simulation
- [ ] Workplace safety training
- [ ] Performance tracking
- [ ] Real-time feedback

### AI Chatbot
- [ ] Natural language queries
- [ ] Safety guidance
- [ ] Emergency responses
- [ ] Multi-language support

### Gamification
- [ ] Score tracking
- [ ] Achievement system
- [ ] Leaderboards
- [ ] Progress monitoring

## 🔍 Debugging Tips

### Common Issues
1. **Server won't start**: Check port 3000 availability
2. **Unity connection fails**: Verify server URL in SafeVerseManager
3. **AR not working**: Check camera permissions and AR support
4. **VR tracking issues**: Ensure proper VR setup and room-scale

### Logging
- Server logs: `logs/safeverse.log`
- Unity logs: Unity Console window
- Browser logs: Developer Tools console

### Performance Monitoring
- Monitor server CPU/memory usage
- Check Unity Profiler for performance bottlenecks
- Use browser dev tools for network analysis

## 📚 Next Steps

1. **Customize Training Scenarios**: Edit VR training modules
2. **Add New Safety Protocols**: Extend AI knowledge base
3. **Integrate IoT Devices**: Connect real sensors
4. **Deploy to Cloud**: Set up production environment
5. **Add Analytics**: Implement usage tracking

## 🆘 Getting Help

- Check `docs/API_DOCUMENTATION.md` for detailed API reference
- Review Unity scripts in `assets/Scripts/`
- Join our development community
- Submit issues on GitHub

Happy building with SafeVerse! 🎉
