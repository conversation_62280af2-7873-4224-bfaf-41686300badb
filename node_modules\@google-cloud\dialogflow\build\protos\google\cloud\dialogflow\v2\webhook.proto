// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.v2;

import "google/cloud/dialogflow/v2/context.proto";
import "google/cloud/dialogflow/v2/intent.proto";
import "google/cloud/dialogflow/v2/session.proto";
import "google/cloud/dialogflow/v2/session_entity_type.proto";
import "google/protobuf/struct.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.V2";
option go_package = "cloud.google.com/go/dialogflow/apiv2/dialogflowpb;dialogflowpb";
option java_multiple_files = true;
option java_outer_classname = "WebhookProto";
option java_package = "com.google.cloud.dialogflow.v2";
option objc_class_prefix = "DF";

// The request message for a webhook call.
message WebhookRequest {
  // The unique identifier of detectIntent request session.
  // Can be used to identify end-user inside webhook implementation.
  // Format: `projects/<Project ID>/agent/sessions/<Session ID>`, or
  // `projects/<Project ID>/agent/environments/<Environment ID>/users/<User
  // ID>/sessions/<Session ID>`.
  string session = 4;

  // The unique identifier of the response. Contains the same value as
  // `[Streaming]DetectIntentResponse.response_id`.
  string response_id = 1;

  // The result of the conversational query or event processing. Contains the
  // same value as `[Streaming]DetectIntentResponse.query_result`.
  QueryResult query_result = 2;

  // Optional. The contents of the original request that was passed to
  // `[Streaming]DetectIntent` call.
  OriginalDetectIntentRequest original_detect_intent_request = 3;
}

// The response message for a webhook call.
//
// This response is validated by the Dialogflow server. If validation fails,
// an error will be returned in the
// [QueryResult.diagnostic_info][google.cloud.dialogflow.v2.QueryResult.diagnostic_info]
// field. Setting JSON fields to an empty value with the wrong type is a common
// error. To avoid this error:
//
// - Use `""` for empty strings
// - Use `{}` or `null` for empty objects
// - Use `[]` or `null` for empty arrays
//
// For more information, see the
// [Protocol Buffers Language
// Guide](https://developers.google.com/protocol-buffers/docs/proto3#json).
message WebhookResponse {
  // Optional. The text response message intended for the end-user.
  // It is recommended to use `fulfillment_messages.text.text[0]` instead.
  // When provided, Dialogflow uses this field to populate
  // [QueryResult.fulfillment_text][google.cloud.dialogflow.v2.QueryResult.fulfillment_text]
  // sent to the integration or API caller.
  string fulfillment_text = 1;

  // Optional. The rich response messages intended for the end-user.
  // When provided, Dialogflow uses this field to populate
  // [QueryResult.fulfillment_messages][google.cloud.dialogflow.v2.QueryResult.fulfillment_messages]
  // sent to the integration or API caller.
  repeated Intent.Message fulfillment_messages = 2;

  // Optional. A custom field used to identify the webhook source.
  // Arbitrary strings are supported.
  // When provided, Dialogflow uses this field to populate
  // [QueryResult.webhook_source][google.cloud.dialogflow.v2.QueryResult.webhook_source]
  // sent to the integration or API caller.
  string source = 3;

  // Optional. This field can be used to pass custom data from your webhook to
  // the integration or API caller. Arbitrary JSON objects are supported. When
  // provided, Dialogflow uses this field to populate
  // [QueryResult.webhook_payload][google.cloud.dialogflow.v2.QueryResult.webhook_payload]
  // sent to the integration or API caller. This field is also used by the
  // [Google Assistant
  // integration](https://cloud.google.com/dialogflow/docs/integrations/aog)
  // for rich response messages.
  // See the format definition at [Google Assistant Dialogflow webhook
  // format](https://developers.google.com/assistant/actions/build/json/dialogflow-webhook-json)
  google.protobuf.Struct payload = 4;

  // Optional. The collection of output contexts that will overwrite currently
  // active contexts for the session and reset their lifespans.
  // When provided, Dialogflow uses this field to populate
  // [QueryResult.output_contexts][google.cloud.dialogflow.v2.QueryResult.output_contexts]
  // sent to the integration or API caller.
  repeated Context output_contexts = 5;

  // Optional. Invokes the supplied events.
  // When this field is set, Dialogflow ignores the `fulfillment_text`,
  // `fulfillment_messages`, and `payload` fields.
  EventInput followup_event_input = 6;

  // Optional. Additional session entity types to replace or extend developer
  // entity types with. The entity synonyms apply to all languages and persist
  // for the session. Setting this data from a webhook overwrites
  // the session entity types that have been set using `detectIntent`,
  // `streamingDetectIntent` or
  // [SessionEntityType][google.cloud.dialogflow.v2.SessionEntityType]
  // management methods.
  repeated SessionEntityType session_entity_types = 10;
}

// Represents the contents of the original request that was passed to
// the `[Streaming]DetectIntent` call.
message OriginalDetectIntentRequest {
  // The source of this request, e.g., `google`, `facebook`, `slack`. It is set
  // by Dialogflow-owned servers.
  string source = 1;

  // Optional. The version of the protocol used for this request.
  // This field is AoG-specific.
  string version = 2;

  // Optional. This field is set to the value of the `QueryParameters.payload`
  // field passed in the request. Some integrations that query a Dialogflow
  // agent may provide additional information in the payload.
  //
  // In particular, for the Dialogflow Phone Gateway integration, this field has
  // the form:
  // <pre>{
  //  "telephony": {
  //    "caller_id": "+18558363987"
  //  }
  // }</pre>
  // Note: The caller ID field (`caller_id`) will be redacted for Trial
  // Edition agents and populated with the caller ID in [E.164
  // format](https://en.wikipedia.org/wiki/E.164) for Essentials Edition agents.
  google.protobuf.Struct payload = 3;
}
