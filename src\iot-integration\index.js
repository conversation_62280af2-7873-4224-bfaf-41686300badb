/**
 * IoT Integration Module
 * Handles real-time sensor data and smart device connectivity
 */

const express = require('express');
const router = express.Router();

class IoTIntegration {
  constructor() {
    this.initialized = false;
    this.connectedDevices = new Map();
    this.sensorThresholds = new Map();
    this.alertRules = new Map();
    this.init();
  }

  async init() {
    try {
      console.log('Initializing IoT Integration...');
      await this.setupSensorThresholds();
      await this.setupAlertRules();
      this.initialized = true;
      console.log('IoT Integration initialized successfully');
    } catch (error) {
      console.error('Failed to initialize IoT Integration:', error);
    }
  }

  async setupSensorThresholds() {
    this.sensorThresholds.set('temperature', {
      normal: { min: 18, max: 25 }, // Celsius
      warning: { min: 15, max: 30 },
      critical: { min: 10, max: 40 }
    });

    this.sensorThresholds.set('humidity', {
      normal: { min: 30, max: 60 }, // Percentage
      warning: { min: 20, max: 70 },
      critical: { min: 10, max: 80 }
    });

    this.sensorThresholds.set('air_quality', {
      normal: { min: 0, max: 50 }, // AQI
      warning: { min: 51, max: 100 },
      critical: { min: 101, max: 500 }
    });

    this.sensorThresholds.set('noise_level', {
      normal: { min: 0, max: 70 }, // Decibels
      warning: { min: 71, max: 85 },
      critical: { min: 86, max: 120 }
    });

    this.sensorThresholds.set('gas_concentration', {
      normal: { min: 0, max: 10 }, // PPM
      warning: { min: 11, max: 50 },
      critical: { min: 51, max: 1000 }
    });
  }

  async setupAlertRules() {
    this.alertRules.set('fire_detection', {
      conditions: [
        { sensor: 'temperature', threshold: 'critical' },
        { sensor: 'smoke_detector', value: true }
      ],
      action: 'immediate_evacuation',
      priority: 'critical'
    });

    this.alertRules.set('gas_leak', {
      conditions: [
        { sensor: 'gas_concentration', threshold: 'warning' }
      ],
      action: 'ventilation_and_evacuation',
      priority: 'high'
    });

    this.alertRules.set('air_quality_alert', {
      conditions: [
        { sensor: 'air_quality', threshold: 'critical' }
      ],
      action: 'respiratory_protection',
      priority: 'medium'
    });
  }

  async processSensorData(data) {
    const { deviceId, sensorType, value, timestamp, location } = data;
    
    try {
      // Store device data
      if (!this.connectedDevices.has(deviceId)) {
        this.connectedDevices.set(deviceId, {
          id: deviceId,
          type: sensorType,
          location,
          lastUpdate: timestamp,
          readings: []
        });
      }

      const device = this.connectedDevices.get(deviceId);
      device.readings.push({ value, timestamp });
      device.lastUpdate = timestamp;

      // Keep only last 100 readings
      if (device.readings.length > 100) {
        device.readings = device.readings.slice(-100);
      }

      // Analyze sensor data for alerts
      const alert = await this.analyzeSensorData(sensorType, value, location, deviceId);
      
      return alert;
    } catch (error) {
      console.error('Error processing sensor data:', error);
      return null;
    }
  }

  async analyzeSensorData(sensorType, value, location, deviceId) {
    const thresholds = this.sensorThresholds.get(sensorType);
    if (!thresholds) return null;

    let alertLevel = 'normal';
    
    if (value < thresholds.critical.min || value > thresholds.critical.max) {
      alertLevel = 'critical';
    } else if (value < thresholds.warning.min || value > thresholds.warning.max) {
      alertLevel = 'warning';
    }

    if (alertLevel !== 'normal') {
      return await this.generateAlert(sensorType, value, alertLevel, location, deviceId);
    }

    return null;
  }

  async generateAlert(sensorType, value, alertLevel, location, deviceId) {
    const alertId = `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const alert = {
      id: alertId,
      type: sensorType,
      level: alertLevel,
      value,
      location,
      deviceId,
      timestamp: new Date().toISOString(),
      message: this.getAlertMessage(sensorType, value, alertLevel),
      recommendations: this.getRecommendations(sensorType, alertLevel),
      actions: this.getRequiredActions(sensorType, alertLevel)
    };

    // Check for compound alerts (multiple sensors triggering)
    const compoundAlert = await this.checkCompoundAlerts(alert);
    if (compoundAlert) {
      return compoundAlert;
    }

    return alert;
  }

  getAlertMessage(sensorType, value, alertLevel) {
    const messages = {
      temperature: {
        warning: `Temperature ${value}°C is outside normal range`,
        critical: `CRITICAL: Extreme temperature detected (${value}°C)`
      },
      humidity: {
        warning: `Humidity ${value}% is outside normal range`,
        critical: `CRITICAL: Extreme humidity detected (${value}%)`
      },
      air_quality: {
        warning: `Air quality index ${value} indicates poor air quality`,
        critical: `CRITICAL: Hazardous air quality detected (AQI: ${value})`
      },
      gas_concentration: {
        warning: `Gas concentration ${value} PPM detected`,
        critical: `CRITICAL: Dangerous gas levels detected (${value} PPM)`
      },
      noise_level: {
        warning: `Noise level ${value} dB exceeds safe limits`,
        critical: `CRITICAL: Dangerous noise levels detected (${value} dB)`
      }
    };

    return messages[sensorType]?.[alertLevel] || `${alertLevel.toUpperCase()}: ${sensorType} reading: ${value}`;
  }

  getRecommendations(sensorType, alertLevel) {
    const recommendations = {
      temperature: {
        warning: ['Check HVAC system', 'Monitor for changes', 'Ensure proper ventilation'],
        critical: ['Evacuate area immediately', 'Check for fire hazards', 'Call emergency services']
      },
      gas_concentration: {
        warning: ['Increase ventilation', 'Check for gas leaks', 'Monitor continuously'],
        critical: ['Evacuate immediately', 'Shut off gas supply', 'Call emergency services']
      },
      air_quality: {
        warning: ['Use air purifiers', 'Limit outdoor activities', 'Wear masks if necessary'],
        critical: ['Evacuate area', 'Use respiratory protection', 'Seek medical attention']
      }
    };

    return recommendations[sensorType]?.[alertLevel] || ['Monitor situation', 'Take appropriate safety measures'];
  }

  getRequiredActions(sensorType, alertLevel) {
    if (alertLevel === 'critical') {
      return ['immediate_notification', 'emergency_protocol', 'evacuation_if_necessary'];
    } else if (alertLevel === 'warning') {
      return ['notify_safety_team', 'monitor_situation', 'prepare_response'];
    }
    return ['log_incident', 'continue_monitoring'];
  }

  async checkCompoundAlerts(newAlert) {
    // Check if multiple sensors are triggering alerts that indicate a specific emergency
    const recentAlerts = this.getRecentAlerts(300000); // Last 5 minutes
    
    // Fire detection logic
    if (newAlert.type === 'temperature' && newAlert.level === 'critical') {
      const smokeAlert = recentAlerts.find(a => a.type === 'smoke_detector');
      if (smokeAlert) {
        return {
          ...newAlert,
          id: `compound_fire_${Date.now()}`,
          type: 'fire_emergency',
          level: 'critical',
          message: 'FIRE EMERGENCY DETECTED: High temperature and smoke detected',
          compoundSensors: ['temperature', 'smoke_detector'],
          actions: ['immediate_evacuation', 'fire_department_notification', 'activate_sprinklers']
        };
      }
    }

    return null;
  }

  getRecentAlerts(timeWindow) {
    // Mock implementation - in real system, this would query alert database
    return [];
  }
}

const iotIntegration = new IoTIntegration();

// API Routes
router.post('/sensor-data', async (req, res) => {
  try {
    const alert = await iotIntegration.processSensorData(req.body);
    res.json({ alert, status: 'processed' });
  } catch (error) {
    res.status(500).json({ error: 'Failed to process sensor data' });
  }
});

router.get('/devices', (req, res) => {
  const devices = {};
  iotIntegration.connectedDevices.forEach((value, key) => {
    devices[key] = value;
  });
  res.json(devices);
});

router.get('/thresholds', (req, res) => {
  const thresholds = {};
  iotIntegration.sensorThresholds.forEach((value, key) => {
    thresholds[key] = value;
  });
  res.json(thresholds);
});

module.exports = {
  router,
  processSensorData: (data) => iotIntegration.processSensorData(data),
  IoTIntegration
};
