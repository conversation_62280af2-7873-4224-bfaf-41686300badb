# SafeVerse API Documentation

## Overview
SafeVerse provides a comprehensive REST API and WebSocket interface for AI-powered AR/VR safety training and assistance.

## Base URL
```
http://localhost:3000/api
```

## Authentication
Most endpoints require authentication. Include the JWT token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## API Endpoints

### Health Check
```http
GET /health
```
Returns server health status and uptime information.

### AI Chatbot Module

#### Send Message to Chatbot
```http
POST /api/chatbot/message
Content-Type: application/json

{
  "message": "How do I use a fire extinguisher?",
  "userId": "user123",
  "context": {
    "environment": "office",
    "location": "building_a_floor_2"
  }
}
```

#### Get Safety Knowledge Base
```http
GET /api/chatbot/knowledge-base
```

### AR Safety Scanner Module

#### Analyze AR Scan for Safety Hazards
```http
POST /api/ar/scan
Content-Type: application/json

{
  "imageData": "base64_encoded_image",
  "location": {
    "latitude": 40.7128,
    "longitude": -74.0060,
    "building": "office_building_a",
    "floor": 2
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "deviceInfo": {
    "type": "mobile",
    "model": "iPhone 14",
    "os": "iOS 16"
  }
}
```

#### Get Hazard Detection Models
```http
GET /api/ar/hazard-models
```

#### Get Safety Standards
```http
GET /api/ar/safety-standards
```

### VR Training Module

#### Start Training Session
```http
POST /api/vr/start-session
Content-Type: application/json

{
  "userId": "user123",
  "scenarioId": "fire_emergency",
  "difficulty": "beginner",
  "customSettings": {
    "environment": "office",
    "enableHints": true,
    "timeLimit": 600
  }
}
```

#### Update Session Progress
```http
POST /api/vr/update-progress/{sessionId}
Content-Type: application/json

{
  "action": "fire_extinguisher_used",
  "result": "success",
  "responseTime": 3500,
  "location": {
    "x": 10.5,
    "y": 2.0,
    "z": 15.3
  }
}
```

#### Get Available Training Scenarios
```http
GET /api/vr/scenarios
```

### Voice & Gesture Control Module

#### Process Voice Command
```http
POST /api/voice-gesture/voice-command
Content-Type: application/json

{
  "audioData": "base64_encoded_audio",
  "transcript": "Where is the nearest fire extinguisher?",
  "userId": "user123",
  "context": {
    "environment": "vr_training",
    "currentLocation": "factory_floor"
  }
}
```

#### Get Available Voice Commands
```http
GET /api/voice-gesture/commands
```

### IoT Integration Module

#### Send Sensor Data
```http
POST /api/iot/sensor-data
Content-Type: application/json

{
  "deviceId": "sensor_001",
  "sensorType": "temperature",
  "value": 35.5,
  "timestamp": "2024-01-15T10:30:00Z",
  "location": {
    "building": "factory_a",
    "floor": 1,
    "room": "production_line_1"
  }
}
```

#### Get Connected Devices
```http
GET /api/iot/devices
```

#### Get Sensor Thresholds
```http
GET /api/iot/thresholds
```

### Gamification Module

#### Get User Profile
```http
GET /api/gamification/profile/{userId}
```

#### Update User Progress
```http
POST /api/gamification/progress/{userId}
Content-Type: application/json

{
  "type": "training_completed",
  "data": {
    "sessionId": "session_123",
    "score": 95,
    "duration": 480000,
    "module": "fire_safety",
    "perfectScore": false
  }
}
```

#### Get Leaderboard
```http
GET /api/gamification/leaderboard/{category}?limit=10
```
Categories: `overall`, `points`, `level`, `streak`, `training_sessions`

#### Get Available Achievements
```http
GET /api/gamification/achievements
```

## WebSocket Events

### Connection
```javascript
const socket = io('http://localhost:3000');
```

### Real-time Chatbot
```javascript
// Send message
socket.emit('chatbot-message', {
  message: 'Emergency help needed',
  userId: 'user123',
  context: { environment: 'factory' }
});

// Receive response
socket.on('chatbot-response', (response) => {
  console.log(response);
});
```

### AR Analysis
```javascript
// Send AR scan
socket.emit('ar-scan', {
  imageData: 'base64_image',
  location: { building: 'office_a', floor: 2 }
});

// Receive analysis
socket.on('ar-analysis', (analysis) => {
  console.log(analysis);
});
```

### VR Training
```javascript
// Start VR session
socket.emit('vr-session-start', {
  userId: 'user123',
  scenarioId: 'fire_emergency'
});

// Session started
socket.on('vr-session-started', (session) => {
  console.log(session);
});
```

### Voice Commands
```javascript
// Send voice command
socket.emit('voice-command', {
  audioData: 'base64_audio',
  userId: 'user123'
});

// Receive response
socket.on('voice-response', (result) => {
  console.log(result);
});
```

### IoT Alerts
```javascript
// Send sensor data
socket.emit('iot-data', {
  deviceId: 'sensor_001',
  sensorType: 'temperature',
  value: 45.0
});

// Receive safety alert
socket.on('safety-alert', (alert) => {
  console.log('SAFETY ALERT:', alert);
});
```

## Error Handling

All API endpoints return errors in the following format:
```json
{
  "error": true,
  "message": "Error description",
  "code": "ERROR_CODE",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Rate Limiting
- 100 requests per 15-minute window per IP
- WebSocket connections limited to 10 per IP

## Response Formats

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "timestamp": "2024-01-15T10:30:00Z"
}
```
