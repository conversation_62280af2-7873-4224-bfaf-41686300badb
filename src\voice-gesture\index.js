/**
 * Voice and Gesture Control System
 * Handles voice recognition and gesture-based interactions
 */

const express = require('express');
const router = express.Router();

class VoiceGestureController {
  constructor() {
    this.initialized = false;
    this.voiceCommands = new Map();
    this.gesturePatterns = new Map();
    this.init();
  }

  async init() {
    try {
      console.log('Initializing Voice & Gesture Controller...');
      await this.loadVoiceCommands();
      await this.loadGesturePatterns();
      this.initialized = true;
      console.log('Voice & Gesture Controller initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Voice & Gesture Controller:', error);
    }
  }

  async loadVoiceCommands() {
    this.voiceCommands.set('emergency', {
      keywords: ['help', 'emergency', 'danger', 'fire', 'accident'],
      action: 'trigger_emergency_protocol',
      response: 'Emergency protocol activated. Stay calm and follow instructions.'
    });

    this.voiceCommands.set('safety_info', {
      keywords: ['how to', 'what should', 'safety', 'procedure'],
      action: 'provide_safety_guidance',
      response: 'I can help with safety procedures. What specific situation do you need help with?'
    });

    this.voiceCommands.set('navigation', {
      keywords: ['where is', 'find', 'locate', 'exit', 'extinguisher'],
      action: 'provide_navigation',
      response: 'I\'ll help you locate that. Please specify what you\'re looking for.'
    });
  }

  async loadGesturePatterns() {
    this.gesturePatterns.set('point', {
      description: 'Pointing gesture to select objects',
      action: 'select_object',
      confidence_threshold: 0.8
    });

    this.gesturePatterns.set('wave', {
      description: 'Wave gesture to get attention',
      action: 'activate_assistant',
      confidence_threshold: 0.7
    });

    this.gesturePatterns.set('stop', {
      description: 'Stop gesture for emergency halt',
      action: 'emergency_stop',
      confidence_threshold: 0.9
    });
  }

  async processVoiceCommand(data) {
    const { audioData, transcript, userId, context } = data;
    
    try {
      const command = this.recognizeCommand(transcript || audioData);
      const response = await this.executeVoiceCommand(command, context);
      
      return {
        recognized_command: command,
        response,
        confidence: 0.9,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error processing voice command:', error);
      return {
        error: true,
        message: 'Failed to process voice command'
      };
    }
  }

  recognizeCommand(input) {
    const lowerInput = input.toLowerCase();
    
    for (const [commandType, commandData] of this.voiceCommands) {
      if (commandData.keywords.some(keyword => lowerInput.includes(keyword))) {
        return {
          type: commandType,
          action: commandData.action,
          response: commandData.response
        };
      }
    }
    
    return {
      type: 'unknown',
      action: 'request_clarification',
      response: 'I didn\'t understand that command. Can you please repeat or rephrase?'
    };
  }

  async executeVoiceCommand(command, context) {
    switch (command.action) {
      case 'trigger_emergency_protocol':
        return await this.handleEmergency(context);
      case 'provide_safety_guidance':
        return await this.provideSafetyGuidance(context);
      case 'provide_navigation':
        return await this.provideNavigation(context);
      default:
        return command.response;
    }
  }

  async handleEmergency(context) {
    return {
      message: 'Emergency protocol activated. Calling for help and providing immediate guidance.',
      actions: ['alert_emergency_services', 'notify_safety_team', 'provide_immediate_guidance'],
      immediate_steps: [
        'Ensure your immediate safety',
        'Move to a safe location if possible',
        'Call emergency services if not already done',
        'Follow evacuation procedures'
      ]
    };
  }

  async provideSafetyGuidance(context) {
    return {
      message: 'I can provide safety guidance for various situations.',
      available_topics: [
        'Fire safety procedures',
        'Workplace safety protocols',
        'Emergency evacuation',
        'First aid basics',
        'PPE requirements'
      ]
    };
  }

  async provideNavigation(context) {
    return {
      message: 'I can help you navigate to safety equipment and exits.',
      available_locations: [
        'Nearest emergency exit',
        'Fire extinguisher locations',
        'First aid stations',
        'Safety equipment storage',
        'Assembly points'
      ]
    };
  }
}

const voiceGestureController = new VoiceGestureController();

// API Routes
router.post('/voice-command', async (req, res) => {
  try {
    const result = await voiceGestureController.processVoiceCommand(req.body);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: 'Failed to process voice command' });
  }
});

router.get('/commands', (req, res) => {
  const commands = {};
  voiceGestureController.voiceCommands.forEach((value, key) => {
    commands[key] = value;
  });
  res.json(commands);
});

module.exports = {
  router,
  processVoiceCommand: (data) => voiceGestureController.processVoiceCommand(data),
  VoiceGestureController
};
