/**
 * AI Chatbot Module
 * Core AI system for natural language processing and safety guidance
 */

const express = require('express');
const router = express.Router();

class AIChatbot {
  constructor() {
    this.initialized = false;
    this.safetyKnowledgeBase = new Map();
    this.conversationHistory = new Map();
    this.init();
  }

  async init() {
    try {
      // Initialize OpenAI or other AI services
      console.log('Initializing AI Chatbot...');
      await this.loadSafetyKnowledgeBase();
      this.initialized = true;
      console.log('AI Chatbot initialized successfully');
    } catch (error) {
      console.error('Failed to initialize AI Chatbot:', error);
    }
  }

  async loadSafetyKnowledgeBase() {
    // Load safety protocols, emergency procedures, and training content
    this.safetyKnowledgeBase.set('fire_safety', {
      procedures: ['Stop, Drop, Roll', 'Call emergency services', 'Evacuate safely'],
      equipment: ['Fire extinguisher', 'Smoke detector', 'Fire blanket'],
      prevention: ['Regular inspections', 'Clear exits', 'Fire drills']
    });

    this.safetyKnowledgeBase.set('workplace_safety', {
      procedures: ['Wear PPE', 'Follow safety protocols', 'Report hazards'],
      equipment: ['Hard hat', 'Safety glasses', 'Gloves', 'Safety boots'],
      prevention: ['Regular training', 'Safety audits', 'Risk assessments']
    });

    this.safetyKnowledgeBase.set('road_safety', {
      procedures: ['Check surroundings', 'Follow traffic rules', 'Use signals'],
      equipment: ['Helmet', 'Reflective vest', 'Lights'],
      prevention: ['Defensive driving', 'Regular maintenance', 'Weather awareness']
    });
  }

  async processMessage(data) {
    const { message, userId, context } = data;
    
    try {
      // Store conversation history
      if (!this.conversationHistory.has(userId)) {
        this.conversationHistory.set(userId, []);
      }
      
      const history = this.conversationHistory.get(userId);
      history.push({ role: 'user', content: message, timestamp: new Date() });

      // Process the message and generate response
      const response = await this.generateResponse(message, context, history);
      
      // Store AI response in history
      history.push({ role: 'assistant', content: response, timestamp: new Date() });
      
      // Keep only last 10 messages to manage memory
      if (history.length > 20) {
        history.splice(0, history.length - 20);
      }

      return {
        response,
        confidence: 0.95,
        suggestions: this.generateSuggestions(context),
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error processing message:', error);
      return {
        response: "I'm sorry, I encountered an error. Please try again.",
        confidence: 0.0,
        error: true
      };
    }
  }

  async generateResponse(message, context, history) {
    // Simple rule-based responses for now
    // TODO: Integrate with OpenAI GPT or other advanced AI models
    
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('fire') || lowerMessage.includes('burn')) {
      return this.getFireSafetyResponse(lowerMessage, context);
    } else if (lowerMessage.includes('workplace') || lowerMessage.includes('factory')) {
      return this.getWorkplaceSafetyResponse(lowerMessage, context);
    } else if (lowerMessage.includes('road') || lowerMessage.includes('traffic')) {
      return this.getRoadSafetyResponse(lowerMessage, context);
    } else if (lowerMessage.includes('emergency') || lowerMessage.includes('help')) {
      return this.getEmergencyResponse(lowerMessage, context);
    } else {
      return "I'm here to help with safety training and guidance. You can ask me about fire safety, workplace safety, road safety, or emergency procedures.";
    }
  }

  getFireSafetyResponse(message, context) {
    const fireSafety = this.safetyKnowledgeBase.get('fire_safety');
    
    if (message.includes('extinguisher')) {
      return "To use a fire extinguisher, remember PASS: Pull the pin, Aim at the base of the fire, Squeeze the handle, and Sweep from side to side. Only use if the fire is small and you have an escape route.";
    } else if (message.includes('evacuation')) {
      return "During fire evacuation: Stay low to avoid smoke, feel doors before opening, use stairs not elevators, and proceed to the designated meeting point. Never go back inside.";
    } else {
      return `Fire safety basics: ${fireSafety.procedures.join(', ')}. Always prioritize your safety and call emergency services immediately.`;
    }
  }

  getWorkplaceSafetyResponse(message, context) {
    const workplaceSafety = this.safetyKnowledgeBase.get('workplace_safety');
    
    if (message.includes('ppe') || message.includes('equipment')) {
      return `Essential PPE includes: ${workplaceSafety.equipment.join(', ')}. Always inspect equipment before use and replace if damaged.`;
    } else {
      return `Workplace safety procedures: ${workplaceSafety.procedures.join(', ')}. Report any hazards immediately to your supervisor.`;
    }
  }

  getRoadSafetyResponse(message, context) {
    const roadSafety = this.safetyKnowledgeBase.get('road_safety');
    
    return `Road safety guidelines: ${roadSafety.procedures.join(', ')}. Always be alert and follow traffic regulations.`;
  }

  getEmergencyResponse(message, context) {
    return "In an emergency: 1) Ensure your immediate safety, 2) Call emergency services (911), 3) Provide clear location and situation details, 4) Follow dispatcher instructions, 5) Stay calm and help others if safe to do so.";
  }

  generateSuggestions(context) {
    const suggestions = [
      "How do I use a fire extinguisher?",
      "What PPE should I wear?",
      "Emergency evacuation procedures",
      "Road safety tips",
      "Workplace hazard reporting"
    ];
    
    return suggestions.slice(0, 3); // Return top 3 suggestions
  }
}

// Initialize chatbot instance
const chatbot = new AIChatbot();

// API Routes
router.post('/message', async (req, res) => {
  try {
    const response = await chatbot.processMessage(req.body);
    res.json(response);
  } catch (error) {
    res.status(500).json({ error: 'Failed to process message' });
  }
});

router.get('/knowledge-base', (req, res) => {
  const knowledgeBase = {};
  chatbot.safetyKnowledgeBase.forEach((value, key) => {
    knowledgeBase[key] = value;
  });
  res.json(knowledgeBase);
});

module.exports = {
  router,
  processMessage: (data) => chatbot.processMessage(data),
  AIChatbot
};
