/**
 * SafeVerse Backend Server
 * Main entry point for the AI-Powered AR/VR Safety Training platform
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
require('dotenv').config();

// Import modules
const aiChatbot = require('./ai-chatbot');
const arModule = require('./ar-module');
const vrModule = require('./vr-module');
const voiceGesture = require('./voice-gesture');
const iotIntegration = require('./iot-integration');
const gamification = require('./gamification');

// Initialize Express app
const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Basic routes
app.get('/', (req, res) => {
  res.json({
    message: 'SafeVerse API Server',
    version: '1.0.0',
    status: 'running',
    modules: {
      aiChatbot: 'active',
      arModule: 'active',
      vrModule: 'active',
      voiceGesture: 'active',
      iotIntegration: 'active',
      gamification: 'active'
    }
  });
});

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// API Routes
app.use('/api/chatbot', aiChatbot.router);
app.use('/api/ar', arModule.router);
app.use('/api/vr', vrModule.router);
app.use('/api/voice-gesture', voiceGesture.router);
app.use('/api/iot', iotIntegration.router);
app.use('/api/gamification', gamification.router);

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);

  // Handle real-time AI chatbot interactions
  socket.on('chatbot-message', async (data) => {
    try {
      const response = await aiChatbot.processMessage(data);
      socket.emit('chatbot-response', response);
    } catch (error) {
      socket.emit('error', { message: 'Chatbot processing failed' });
    }
  });

  // Handle AR safety scanning
  socket.on('ar-scan', async (data) => {
    try {
      const analysis = await arModule.analyzeSafetyHazards(data);
      socket.emit('ar-analysis', analysis);
    } catch (error) {
      socket.emit('error', { message: 'AR analysis failed' });
    }
  });

  // Handle VR training sessions
  socket.on('vr-session-start', async (data) => {
    try {
      const session = await vrModule.startTrainingSession(data);
      socket.emit('vr-session-started', session);
    } catch (error) {
      socket.emit('error', { message: 'VR session start failed' });
    }
  });

  // Handle voice commands
  socket.on('voice-command', async (data) => {
    try {
      const result = await voiceGesture.processVoiceCommand(data);
      socket.emit('voice-response', result);
    } catch (error) {
      socket.emit('error', { message: 'Voice processing failed' });
    }
  });

  // Handle IoT sensor data
  socket.on('iot-data', async (data) => {
    try {
      const alert = await iotIntegration.processSensorData(data);
      if (alert) {
        socket.emit('safety-alert', alert);
      }
    } catch (error) {
      socket.emit('error', { message: 'IoT processing failed' });
    }
  });

  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Something went wrong!',
    message: err.message
  });
});

// Start server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`SafeVerse server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
});

module.exports = { app, server, io };
