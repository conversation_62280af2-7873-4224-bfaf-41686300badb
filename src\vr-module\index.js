/**
 * VR Training Simulation Engine
 * Handles immersive VR training scenarios and performance tracking
 */

const express = require('express');
const router = express.Router();

class VRTrainingEngine {
  constructor() {
    this.initialized = false;
    this.trainingScenarios = new Map();
    this.activeSessions = new Map();
    this.performanceMetrics = new Map();
    this.init();
  }

  async init() {
    try {
      console.log('Initializing VR Training Engine...');
      await this.loadTrainingScenarios();
      this.initialized = true;
      console.log('VR Training Engine initialized successfully');
    } catch (error) {
      console.error('Failed to initialize VR Training Engine:', error);
    }
  }

  async loadTrainingScenarios() {
    // Fire Safety Training
    this.trainingScenarios.set('fire_emergency', {
      title: 'Fire Emergency Response',
      description: 'Learn proper fire evacuation and extinguisher use',
      duration: 600, // 10 minutes
      difficulty: 'beginner',
      objectives: [
        'Identify fire hazards',
        'Use fire extinguisher correctly',
        'Execute safe evacuation',
        'Assist others in emergency'
      ],
      environments: ['office', 'kitchen', 'factory'],
      equipment: ['fire_extinguisher', 'smoke_detector', 'emergency_exit']
    });

    // Workplace Safety Training
    this.trainingScenarios.set('workplace_safety', {
      title: 'Industrial Workplace Safety',
      description: 'Practice safe procedures in industrial environments',
      duration: 900, // 15 minutes
      difficulty: 'intermediate',
      objectives: [
        'Proper PPE usage',
        'Hazard identification',
        'Safe equipment operation',
        'Emergency procedures'
      ],
      environments: ['factory_floor', 'construction_site', 'warehouse'],
      equipment: ['hard_hat', 'safety_glasses', 'gloves', 'safety_harness']
    });

    // Road Safety Training
    this.trainingScenarios.set('road_safety', {
      title: 'Road Safety and Traffic Rules',
      description: 'Practice defensive driving and pedestrian safety',
      duration: 720, // 12 minutes
      difficulty: 'beginner',
      objectives: [
        'Follow traffic signals',
        'Maintain safe distances',
        'Handle emergency situations',
        'Pedestrian awareness'
      ],
      environments: ['city_street', 'highway', 'intersection'],
      equipment: ['vehicle', 'traffic_lights', 'road_signs']
    });
  }

  async startTrainingSession(data) {
    const { userId, scenarioId, difficulty, customSettings } = data;
    
    try {
      const scenario = this.trainingScenarios.get(scenarioId);
      if (!scenario) {
        throw new Error('Training scenario not found');
      }

      const sessionId = this.generateSessionId();
      const session = {
        id: sessionId,
        userId,
        scenarioId,
        scenario,
        startTime: new Date(),
        status: 'active',
        progress: 0,
        currentObjective: 0,
        performance: {
          score: 0,
          completedObjectives: [],
          mistakes: [],
          responseTime: [],
          safetyViolations: []
        },
        settings: {
          difficulty: difficulty || scenario.difficulty,
          environment: customSettings?.environment || scenario.environments[0],
          ...customSettings
        }
      };

      this.activeSessions.set(sessionId, session);
      
      return {
        sessionId,
        scenario: {
          title: scenario.title,
          description: scenario.description,
          objectives: scenario.objectives,
          estimatedDuration: scenario.duration
        },
        initialInstructions: this.getInitialInstructions(scenario),
        environment: session.settings.environment
      };
    } catch (error) {
      console.error('Error starting training session:', error);
      throw error;
    }
  }

  getInitialInstructions(scenario) {
    const instructions = {
      'fire_emergency': [
        'Welcome to Fire Emergency Training',
        'You will learn to respond to fire emergencies safely',
        'Follow the AI guide and complete each objective',
        'Remember: Safety first, then action'
      ],
      'workplace_safety': [
        'Welcome to Workplace Safety Training',
        'Practice safe procedures in industrial environments',
        'Wear appropriate PPE at all times',
        'Report any hazards you observe'
      ],
      'road_safety': [
        'Welcome to Road Safety Training',
        'Learn defensive driving and traffic awareness',
        'Follow all traffic rules and signals',
        'Stay alert for pedestrians and other vehicles'
      ]
    };

    return instructions[scenario.id] || ['Welcome to Safety Training', 'Follow the instructions carefully'];
  }

  async updateSessionProgress(sessionId, progressData) {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    const { action, result, responseTime, location } = progressData;
    
    // Update progress based on action
    if (result === 'success') {
      session.performance.score += 10;
      session.progress = Math.min(100, session.progress + (100 / session.scenario.objectives.length));
      
      if (action === 'objective_completed') {
        session.performance.completedObjectives.push({
          objective: session.scenario.objectives[session.currentObjective],
          completedAt: new Date(),
          responseTime
        });
        session.currentObjective++;
      }
    } else if (result === 'mistake') {
      session.performance.mistakes.push({
        action,
        location,
        timestamp: new Date(),
        impact: progressData.impact || 'minor'
      });
      session.performance.score = Math.max(0, session.performance.score - 5);
    }

    // Check if session is complete
    if (session.currentObjective >= session.scenario.objectives.length) {
      session.status = 'completed';
      session.endTime = new Date();
      await this.finalizeSession(session);
    }

    return {
      progress: session.progress,
      currentScore: session.performance.score,
      nextObjective: session.scenario.objectives[session.currentObjective],
      feedback: this.generateFeedback(action, result),
      sessionComplete: session.status === 'completed'
    };
  }

  async finalizeSession(session) {
    const duration = session.endTime - session.startTime;
    const finalScore = this.calculateFinalScore(session.performance, duration);
    
    const sessionSummary = {
      sessionId: session.id,
      userId: session.userId,
      scenario: session.scenarioId,
      duration: duration,
      finalScore,
      objectivesCompleted: session.performance.completedObjectives.length,
      totalObjectives: session.scenario.objectives.length,
      mistakes: session.performance.mistakes.length,
      safetyViolations: session.performance.safetyViolations.length,
      completedAt: session.endTime,
      certification: finalScore >= 80 ? 'passed' : 'needs_improvement'
    };

    // Store performance metrics
    if (!this.performanceMetrics.has(session.userId)) {
      this.performanceMetrics.set(session.userId, []);
    }
    this.performanceMetrics.get(session.userId).push(sessionSummary);

    return sessionSummary;
  }

  calculateFinalScore(performance, duration) {
    let score = performance.score;
    
    // Bonus for completing all objectives
    if (performance.completedObjectives.length === performance.completedObjectives.length) {
      score += 20;
    }
    
    // Penalty for mistakes and safety violations
    score -= (performance.mistakes.length * 5);
    score -= (performance.safetyViolations.length * 10);
    
    // Time bonus (if completed quickly)
    const avgResponseTime = performance.responseTime.reduce((a, b) => a + b, 0) / performance.responseTime.length;
    if (avgResponseTime < 5000) { // Less than 5 seconds average
      score += 10;
    }

    return Math.max(0, Math.min(100, score));
  }

  generateFeedback(action, result) {
    const feedbackMessages = {
      'fire_extinguisher_used': {
        'success': 'Excellent! You used the fire extinguisher correctly using the PASS technique.',
        'mistake': 'Remember to aim at the base of the fire, not the flames.'
      },
      'evacuation_route': {
        'success': 'Good job following the evacuation route and staying low.',
        'mistake': 'Always use stairs during evacuation, never elevators.'
      },
      'ppe_equipped': {
        'success': 'Perfect! You equipped all necessary PPE before entering the area.',
        'mistake': 'You forgot to wear safety glasses. Always check PPE requirements.'
      }
    };

    return feedbackMessages[action]?.[result] || 'Continue following safety procedures.';
  }

  generateSessionId() {
    return `vr_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Initialize VR training engine
const vrEngine = new VRTrainingEngine();

// API Routes
router.post('/start-session', async (req, res) => {
  try {
    const session = await vrEngine.startTrainingSession(req.body);
    res.json(session);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.post('/update-progress/:sessionId', async (req, res) => {
  try {
    const progress = await vrEngine.updateSessionProgress(req.params.sessionId, req.body);
    res.json(progress);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.get('/scenarios', (req, res) => {
  const scenarios = {};
  vrEngine.trainingScenarios.forEach((value, key) => {
    scenarios[key] = value;
  });
  res.json(scenarios);
});

module.exports = {
  router,
  startTrainingSession: (data) => vrEngine.startTrainingSession(data),
  VRTrainingEngine
};
