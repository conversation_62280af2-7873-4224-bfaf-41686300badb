#!/bin/bash

# SafeVerse Setup Script
# This script sets up the development environment for SafeVerse

echo "🌟 Setting up SafeVerse: AI-Powered AR/VR Safety Training & Assistance Bot"
echo "=================================================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8+ and try again."
    exit 1
fi

echo "✅ Prerequisites check passed"

# Install Node.js dependencies
echo "📦 Installing Node.js dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install Node.js dependencies"
    exit 1
fi

# Create Python virtual environment
echo "🐍 Setting up Python virtual environment..."
python3 -m venv venv

# Activate virtual environment and install Python dependencies
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    # Windows
    source venv/Scripts/activate
else
    # Unix/Linux/macOS
    source venv/bin/activate
fi

echo "📦 Installing Python dependencies..."
pip install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "❌ Failed to install Python dependencies"
    exit 1
fi

# Create environment file
echo "⚙️ Setting up environment configuration..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "📝 Created .env file from template. Please update with your API keys and configuration."
else
    echo "📝 .env file already exists"
fi

# Create logs directory
mkdir -p logs

# Create Unity project directories
echo "🎮 Setting up Unity project structure..."
mkdir -p Unity/SafeVerse/Assets/Scripts
mkdir -p Unity/SafeVerse/Assets/Scenes
mkdir -p Unity/SafeVerse/Assets/Prefabs
mkdir -p Unity/SafeVerse/Assets/Materials
mkdir -p Unity/SafeVerse/Assets/Textures
mkdir -p Unity/SafeVerse/Assets/Audio
mkdir -p Unity/SafeVerse/Assets/Models

# Copy Unity script to proper location
if [ -f "assets/Scripts/SafeVerseManager.cs" ]; then
    cp assets/Scripts/SafeVerseManager.cs Unity/SafeVerse/Assets/Scripts/
    echo "📋 Copied Unity scripts to project"
fi

# Create test directories
mkdir -p tests/unit
mkdir -p tests/integration
mkdir -p tests/e2e

echo ""
echo "🎉 SafeVerse setup completed successfully!"
echo ""
echo "📋 Next Steps:"
echo "1. Update .env file with your API keys and configuration"
echo "2. Start the development server: npm run dev"
echo "3. Open Unity and create a new project in the Unity/SafeVerse directory"
echo "4. Import required Unity packages (AR Foundation, XR Interaction Toolkit, etc.)"
echo "5. Configure your AR/VR SDKs (ARCore, ARKit, Oculus SDK, etc.)"
echo ""
echo "📚 Documentation:"
echo "- API Documentation: docs/API_DOCUMENTATION.md"
echo "- Unity Setup: ProjectSettings.md"
echo "- README: README.md"
echo ""
echo "🚀 To start the server:"
echo "   npm start"
echo ""
echo "🧪 To run tests:"
echo "   npm test"
echo ""
echo "Happy coding! 🎯"
