using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.XR.ARFoundation;
using UnityEngine.XR.Interaction.Toolkit;
using SocketIOClient;
using Newtonsoft.Json;

namespace SafeVerse
{
    /// <summary>
    /// Main manager class for SafeVerse AR/VR Safety Training system
    /// Handles communication between Unity and the backend server
    /// </summary>
    public class SafeVerseManager : MonoBehaviour
    {
        [Header("Server Configuration")]
        public string serverUrl = "http://localhost:3000";
        public bool autoConnect = true;
        
        [Header("User Settings")]
        public string userId = "user123";
        public string currentEnvironment = "office";
        
        [Header("AR Components")]
        public ARCamera arCamera;
        public ARPlaneManager arPlaneManager;
        public ARRaycastManager arRaycastManager;
        
        [Header("VR Components")]
        public XRRig xrRig;
        public XRInteractionManager interactionManager;
        
        [Header("UI Components")]
        public GameObject chatbotUI;
        public GameObject alertUI;
        public GameObject progressUI;
        
        // Private fields
        private SocketIOUnity socket;
        private bool isConnected = false;
        private Queue<string> messageQueue = new Queue<string>();
        
        // Events
        public event Action<string> OnChatbotResponse;
        public event Action<SafetyAlert> OnSafetyAlert;
        public event Action<TrainingProgress> OnTrainingProgress;
        
        void Start()
        {
            InitializeSafeVerse();
        }
        
        void InitializeSafeVerse()
        {
            Debug.Log("Initializing SafeVerse...");
            
            if (autoConnect)
            {
                ConnectToServer();
            }
            
            // Initialize AR/VR components based on platform
            InitializePlatformComponents();
        }
        
        void InitializePlatformComponents()
        {
            #if UNITY_ANDROID || UNITY_IOS
            // Mobile AR initialization
            if (arCamera != null)
            {
                arCamera.enabled = true;
                Debug.Log("AR components initialized for mobile");
            }
            #elif UNITY_STANDALONE_WIN
            // Desktop VR initialization
            if (xrRig != null)
            {
                xrRig.enabled = true;
                Debug.Log("VR components initialized for desktop");
            }
            #endif
        }
        
        public void ConnectToServer()
        {
            try
            {
                var uri = new Uri(serverUrl);
                socket = new SocketIOUnity(uri);
                
                socket.OnConnected += OnServerConnected;
                socket.OnDisconnected += OnServerDisconnected;
                socket.OnError += OnServerError;
                
                // Register event handlers
                socket.On("chatbot-response", OnChatbotResponseReceived);
                socket.On("ar-analysis", OnARAnalysisReceived);
                socket.On("vr-session-started", OnVRSessionStarted);
                socket.On("safety-alert", OnSafetyAlertReceived);
                socket.On("voice-response", OnVoiceResponseReceived);
                
                socket.Connect();
                Debug.Log("Connecting to SafeVerse server...");
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to connect to server: {e.Message}");
            }
        }
        
        void OnServerConnected(object sender, EventArgs e)
        {
            isConnected = true;
            Debug.Log("Connected to SafeVerse server");
            
            // Process any queued messages
            while (messageQueue.Count > 0)
            {
                var message = messageQueue.Dequeue();
                // Process queued message
            }
        }
        
        void OnServerDisconnected(object sender, string e)
        {
            isConnected = false;
            Debug.Log("Disconnected from SafeVerse server");
        }
        
        void OnServerError(object sender, string e)
        {
            Debug.LogError($"Server error: {e}");
        }
        
        // Chatbot functionality
        public void SendChatbotMessage(string message)
        {
            if (!isConnected)
            {
                messageQueue.Enqueue(message);
                return;
            }
            
            var data = new
            {
                message = message,
                userId = userId,
                context = new
                {
                    environment = currentEnvironment,
                    platform = Application.platform.ToString(),
                    timestamp = DateTime.UtcNow.ToString("O")
                }
            };
            
            socket.Emit("chatbot-message", data);
        }
        
        void OnChatbotResponseReceived(SocketIOResponse response)
        {
            try
            {
                var responseData = response.GetValue<ChatbotResponse>();
                OnChatbotResponse?.Invoke(responseData.response);
                Debug.Log($"Chatbot response: {responseData.response}");
            }
            catch (Exception e)
            {
                Debug.LogError($"Error processing chatbot response: {e.Message}");
            }
        }
        
        // AR Safety Scanner functionality
        public void PerformARScan()
        {
            if (!isConnected) return;
            
            StartCoroutine(CaptureAndAnalyzeAR());
        }
        
        IEnumerator CaptureAndAnalyzeAR()
        {
            // Capture camera image
            var texture = new Texture2D(Screen.width, Screen.height, TextureFormat.RGB24, false);
            yield return new WaitForEndOfFrame();
            texture.ReadPixels(new Rect(0, 0, Screen.width, Screen.height), 0, 0);
            texture.Apply();
            
            // Convert to base64
            byte[] imageBytes = texture.EncodeToPNG();
            string base64Image = Convert.ToBase64String(imageBytes);
            
            var scanData = new
            {
                imageData = base64Image,
                location = new
                {
                    building = "office_building_a",
                    floor = 2,
                    timestamp = DateTime.UtcNow.ToString("O")
                },
                deviceInfo = new
                {
                    type = "mobile",
                    model = SystemInfo.deviceModel,
                    os = SystemInfo.operatingSystem
                }
            };
            
            socket.Emit("ar-scan", scanData);
            
            Destroy(texture);
        }
        
        void OnARAnalysisReceived(SocketIOResponse response)
        {
            try
            {
                var analysis = response.GetValue<ARAnalysis>();
                ProcessARAnalysis(analysis);
            }
            catch (Exception e)
            {
                Debug.LogError($"Error processing AR analysis: {e.Message}");
            }
        }
        
        void ProcessARAnalysis(ARAnalysis analysis)
        {
            Debug.Log($"AR Analysis received - Safety Score: {analysis.safety_score}");
            
            // Display AR overlays for detected hazards
            foreach (var hazard in analysis.detected_hazards)
            {
                CreateHazardOverlay(hazard);
            }
            
            // Show recommendations
            DisplayRecommendations(analysis.recommendations);
        }
        
        void CreateHazardOverlay(DetectedHazard hazard)
        {
            // Create AR overlay for hazard visualization
            // This would involve placing 3D objects or UI elements in AR space
            Debug.Log($"Creating overlay for hazard: {hazard.type} at {hazard.location}");
        }
        
        // VR Training functionality
        public void StartVRTraining(string scenarioId)
        {
            if (!isConnected) return;
            
            var sessionData = new
            {
                userId = userId,
                scenarioId = scenarioId,
                difficulty = "beginner",
                customSettings = new
                {
                    environment = currentEnvironment,
                    enableHints = true
                }
            };
            
            socket.Emit("vr-session-start", sessionData);
        }
        
        void OnVRSessionStarted(SocketIOResponse response)
        {
            try
            {
                var session = response.GetValue<VRSession>();
                Debug.Log($"VR Training session started: {session.scenario.title}");
                // Initialize VR training environment
            }
            catch (Exception e)
            {
                Debug.LogError($"Error starting VR session: {e.Message}");
            }
        }
        
        // Safety Alert handling
        void OnSafetyAlertReceived(SocketIOResponse response)
        {
            try
            {
                var alert = response.GetValue<SafetyAlert>();
                OnSafetyAlert?.Invoke(alert);
                DisplaySafetyAlert(alert);
            }
            catch (Exception e)
            {
                Debug.LogError($"Error processing safety alert: {e.Message}");
            }
        }
        
        void DisplaySafetyAlert(SafetyAlert alert)
        {
            Debug.LogWarning($"SAFETY ALERT: {alert.message}");
            
            // Show alert UI
            if (alertUI != null)
            {
                alertUI.SetActive(true);
                // Update alert UI with alert information
            }
        }
        
        // Voice command functionality
        void OnVoiceResponseReceived(SocketIOResponse response)
        {
            try
            {
                var voiceResponse = response.GetValue<VoiceResponse>();
                Debug.Log($"Voice response: {voiceResponse.response}");
            }
            catch (Exception e)
            {
                Debug.LogError($"Error processing voice response: {e.Message}");
            }
        }
        
        void DisplayRecommendations(List<Recommendation> recommendations)
        {
            foreach (var recommendation in recommendations)
            {
                Debug.Log($"Recommendation: {recommendation.action} - {recommendation.description}");
            }
        }
        
        void OnDestroy()
        {
            if (socket != null)
            {
                socket.Disconnect();
                socket.Dispose();
            }
        }
    }
    
    // Data classes for JSON serialization
    [Serializable]
    public class ChatbotResponse
    {
        public string response;
        public float confidence;
        public List<string> suggestions;
    }
    
    [Serializable]
    public class ARAnalysis
    {
        public string analysis_id;
        public int safety_score;
        public List<DetectedHazard> detected_hazards;
        public List<Recommendation> recommendations;
    }
    
    [Serializable]
    public class DetectedHazard
    {
        public string type;
        public float confidence;
        public HazardLocation location;
        public string severity;
        public string description;
    }
    
    [Serializable]
    public class HazardLocation
    {
        public float x, y, width, height;
    }
    
    [Serializable]
    public class Recommendation
    {
        public string priority;
        public string action;
        public string description;
    }
    
    [Serializable]
    public class VRSession
    {
        public string sessionId;
        public VRScenario scenario;
        public List<string> initialInstructions;
    }
    
    [Serializable]
    public class VRScenario
    {
        public string title;
        public string description;
        public List<string> objectives;
    }
    
    [Serializable]
    public class SafetyAlert
    {
        public string id;
        public string type;
        public string level;
        public string message;
        public List<string> recommendations;
    }
    
    [Serializable]
    public class VoiceResponse
    {
        public string response;
        public float confidence;
    }
    
    [Serializable]
    public class TrainingProgress
    {
        public float progress;
        public int currentScore;
        public string nextObjective;
        public string feedback;
    }
}
